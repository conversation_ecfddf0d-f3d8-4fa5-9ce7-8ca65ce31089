"""Write Sparameters with Lumerical FDTD."""

from __future__ import annotations

import shutil
import time
from typing import TYPE_CHECKING, cast

import gdsfactory as gf
import numpy as np
import yaml
from gdsfactory import logger
from gdsfactory.config import __version__
from gdsfactory.generic_tech.simulation_settings import (
    SIMULATION_SETTINGS_LUMERICAL_FDTD,
    SimulationSettingsLumericalFdtd,
)
from gdsfactory.pdk import get_layer_stack
from gdsfactory.technology import LayerStack
from gdsfactory.technology.layer_stack import Derived<PERSON>ayer, LogicalLayer
from gdsfactory.typings import LayerEnum
from simulation.mode_clarify import *
from gplugins.common.utils.get_sparameters_path import (
    get_sparameters_path_lumerical as get_sparameters_path,
)

if TYPE_CHECKING:
    from gdsfactory.typings import ComponentSpec, MaterialSpec, PathType

run_false_warning = """
You have passed run=False to debug the simulation

run=False returns the simulation session for you to debug and make sure it is correct

To compute the Sparameters you need to pass run=True
"""
#这两个函数的合并，material name的添加还没搞
def set_material(session, structure: str, material: MaterialSpec, material_name: str) -> None:
    """Sets the material of a structure.

    Args:
        session: lumerical session.
        structure: name of the lumerical structure.
        material: material spec, can be
            a string from lumerical database materials.
            a float or int, representing refractive index.
            a complex for n, k materials.
            a dict for Sellmeier coefficients (isotropic or anisotropic).
    """
    if isinstance(material, str):
        session.setnamed(structure, "material", material)
    elif isinstance(material, int | float):
        session.setnamed(structure, "index", material)
    elif isinstance(material, complex|tuple|list|dict):
        try:
            session.getmaterial(material_name)
            session.setnamed(structure, "material", material_name)
            return
        except Exception:
            if isinstance(material, complex):
                material = (material.real, material.imag)
                mat = session.addmaterial("(n,k) Material")
                session.setmaterial(mat, "Refractive Index", material.real)
                session.setmaterial(mat, "Imaginary Refractive Index", material.imag)
                session.setnamed(structure, "material", mat)
            elif isinstance(material, tuple | list):
                if len(material) != 2:
                    raise ValueError(
                    "Complex material requires a tuple or list of two numbers "
                    f"(real, imag). Got {material} "
                )
                real, imag = material
                mat = session.addmaterial("(n,k) Material")
                session.setmaterial(mat, "Refractive Index", real)
                session.setmaterial(mat, "Imaginary Refractive Index", imag)
                session.setnamed(structure, "material", mat)
            elif isinstance(material, dict):
                # Sellmeier coefficients: isotropic or anisotropic
                # material = {
                #     "B1": [1.03961212],or [1,2,3]
                mat = session.addmaterial("Sellmeier")
                session.setmaterial(mat, "name", material_name)
                if len(material['B1'])==3:
                    session.setmaterial(material_name, "Anisotropy", 1) 
                for k, v in material.items():
                    command = f"setmaterial('{material_name}', '{k}', {v});"
                    session.eval(command)
                session.setnamed(structure, "material", material_name)
    else:
        raise ValueError(
            f"{material!r} needs to be a float refractive index, a complex number, tuple, "
            "dict for Sellmeier coefficients, or a string from lumerical's material database"
        )
def set_material_layer(session, layer_name: str, material: MaterialSpec, material_name: str) -> None:
    """Sets the material of a structure.

    Args:
        session: lumerical session.
        layer_name: name of the lumerical layer.
        material: material spec, can be
            a string from lumerical database materials.
            a float or int, representing refractive index.
            a complex for n, k materials.
            a dict for Sellmeier coefficients (isotropic or anisotropic).
    """
    if isinstance(material, str):
        session.setlayer(layer_name, "pattern material", material)
    elif isinstance(material, int | float):
        session.setlayer(layer_name, "pattern material", material)
    elif isinstance(material, complex|tuple|list|dict):
        try:
            session.getmaterial(material_name)
            session.setlayer(layer_name, "pattern material", material_name)
            return
        except Exception:
            if isinstance(material, complex):
                material = (material.real, material.imag)
                mat = session.addmaterial("(n,k) Material")
                session.setmaterial(mat, "Refractive Index", material.real)
                session.setmaterial(mat, "Imaginary Refractive Index", material.imag)
                session.setlayer(layer_name, "pattern material", mat)
            elif isinstance(material, tuple | list):
                if len(material) != 2:
                    raise ValueError(
                    "Complex material requires a tuple or list of two numbers "
                    f"(real, imag). Got {material} "
                )
                real, imag = material
                mat = session.addmaterial("(n,k) Material")
                session.setmaterial(mat, "Refractive Index", real)
                session.setmaterial(mat, "Imaginary Refractive Index", imag)
                session.setlayer(layer_name, "pattern material", mat)
            elif isinstance(material, dict):
                # Sellmeier coefficients: isotropic or anisotropic
                # material = {
                #     "B1": [1.03961212],or [1,2,3]
                mat = session.addmaterial("Sellmeier")
                session.setmaterial(mat, "name", material_name)
                if len(material['B1'])==3:
                    session.setmaterial(material_name, "Anisotropy", 1) 
                for k, v in material.items():
                    command = f"setmaterial('{material_name}', '{k}', {v});"
                    session.eval(command)
                session.setlayer(layer_name, "pattern material", material_name)
    else:    
        raise ValueError(
            f"{material!r} needs to be a float refractive index, a complex number, tuple, "
            "dict for Sellmeier coefficients, or a string from lumerical's material database"
        )


def write_sparameters_lumerical(
    component: ComponentSpec,
    session: object | None = None,
    run: bool = True,
    overwrite: bool = False,
    dirpath: PathType | None = None,
    layer_stack: LayerStack | None = None,
    simulation_settings: SimulationSettingsLumericalFdtd = SIMULATION_SETTINGS_LUMERICAL_FDTD,
    material_name_to_lumerical: dict[str, MaterialSpec] | None = None,
    delete_fsp_files: bool = False,
    xmargin: float = 0,
    ymargin: float = 0,
    xmargin_left: float | None = None,
    xmargin_right: float | None = None,
    ymargin_top: float | None = None,
    ymargin_bot: float | None = None,
    zmargin: float = 1.0,
    exclude_layers: list[int] | None = None,
    **settings,
) -> np.ndarray:
    r"""Returns and writes component Sparameters using Lumerical FDTD.

    If simulation exists it returns the Sparameters directly unless overwrite=True
    which forces a re-run of the simulation

    Writes Sparameters both in .npz and .DAT (interconnect format) as well as
    simulation settings in .YAML

    In the npz format you can see `S12m` where `m` stands for magnitude
    and `S12a` where `a` stands for angle in radians

    Your components need to have ports, that will extend over the PML.

    .. image:: https://i.imgur.com/dHAzZRw.png

    For your Fab technology you can overwrite

    - simulation_settings
    - dirpath
    - layerStack

    converts gdsfactory units (um) to Lumerical units (m)

    Disclaimer: This function tries to extract Sparameters automatically
    is hard to make a function that will fit all your possible simulation settings.
    You can use this function for inspiration to create your own.

    Args:
        component: Component to simulate.
        session: you can pass a session=lumapi.FDTD() or it will create one.
        run: True runs Lumerical, False only draws simulation.
        overwrite: run even if simulation results already exists.
        dirpath: directory to store sparameters in npz.
            Defaults to active Pdk.sparameters_path.
        layer_stack: contains layer to thickness, zmin and material.
            Defaults to active pdk.layer_stack.
        simulation_settings: dataclass with all simulation_settings. 这里改背景材料等设置
        material_name_to_lumerical: alias to lumerical material's database name
            or refractive index.
            translate material name in LayerStack to lumerical's database name.
        delete_fsp_files: deletes lumerical fsp files after simulation.
        xmargin: left/right distance from component to PML.
        xmargin_left: left distance from component to PML.
        xmargin_right: right distance from component to PML.
        ymargin: left/right distance from component to PML.
        ymargin_top: top distance from component to PML.
        ymargin_bot: bottom distance from component to PML.
        zmargin: thickness for cladding above and below core.
        exclude_layers: list of layer indices to exclude from simulation.
        settings: additional simulation settings to overwrite

    Keyword Args:
        background_material: for the background.
        port_margin: on both sides of the port width (um).
        port_height: port height (um).
        port_extension: port extension (um).
        mesh_accuracy: 2 (1: coarse, 2: fine, 3: superfine).
        wavelength_start: 1.2 (um).
        wavelength_stop: 1.6 (um).
        wavelength_points: 500.
        simulation_time: (s) related to max path length 3e8/2.4*10e-12*1e6 = 1.25mm.
        simulation_temperature: in kelvin (default = 300).
        frequency_dependent_profile: computes mode profiles for different wavelengths.
        field_profile_samples: number of wavelengths to compute field profile.


    .. code::

         top view
              ________________________________
             |                               |
             | xmargin                       | port_extension
             |<------>          port_margin ||<-->
          o2_|___________          _________||_o3
             |           \        /          |
             |            \      /           |
             |             ======            |
             |            /      \           |
          o1_|___________/        \__________|_o4
             |   |                           |
             |   |ymargin                    |
             |   |                           |
             |___|___________________________|

        side view
              ________________________________
             |                               |
             |                               |
             |                               |
             |ymargin                        |
             |<---> _____         _____      |
             |     |     |       |     |     |
             |     |     |       |     |     |
             |     |_____|       |_____|     |
             |       |                       |
             |       |                       |
             |       |zmargin                |
             |       |                       |
             |_______|_______________________|



    Return:
        Sparameters np.ndarray (wavelengths, o1@0,o1@0, o1@0,o2@0 ...)
            suffix `a` for angle in radians and `m` for module.

    """
    layer_stack = layer_stack or get_layer_stack()
    component = component
    sim_settings = dict(simulation_settings)


    xmargin_left = xmargin_left or xmargin
    xmargin_right = xmargin_right or xmargin
    ymargin_top = ymargin_top or ymargin
    ymargin_bot = ymargin_bot or ymargin

    layer_to_thickness = layer_stack.get_layer_to_thickness()
    layer_to_zmin = layer_stack.get_layer_to_zmin()
    layer_to_material = layer_stack.get_layer_to_material()
    layer_to_sidewall = layer_stack.get_layer_to_sidewall_angle()
    #这里也可以传递模拟设置
    if hasattr(component.info, "simulation_settings"):
        sim_settings |= component.info.simulation_settings
        logger.info(
            f"Updating {component.name!r} sim settings {component.simulation_settings}"
        )
    for setting in settings:
        if setting not in sim_settings:
            raise ValueError(
                f"Invalid setting {setting!r} not in ({list(sim_settings.keys())})"
            )
    #sim_settings看起来是用来保存模拟设置的，实际运行是用ss
    sim_settings.update(**settings)
    ss = SimulationSettingsLumericalFdtd(**sim_settings)

    component_with_booleans = layer_stack.get_component_with_derived_layers(component)
    component_with_padding = gf.add_padding_container(
        component_with_booleans,
        default=0,
        top=ymargin_top,
        bottom=ymargin_bot,
        left=xmargin_left,
        right=xmargin_right,
    )

    component_extended = gf.components.extend_ports(
        component_with_padding, length=ss.distance_monitors_to_pml
    )

    ports = component.ports.filter(port_type="optical")
    if not ports:
        raise ValueError(f"{component.name!r} does not have any optical ports")

    component_extended_beyond_pml = gf.components.extension.extend_ports(
        component=component_extended, length=ss.port_extension
    )
    component_extended_beyond_pml = component_extended_beyond_pml.copy()
    component_extended_beyond_pml.flatten()
    #component_extended_beyond_pml.name = "top"
    timestamp = str(int(time.time() * 1000000))[-8:]
    component_extended_beyond_pml.name = f"top_{timestamp}"
    gdspath = component_extended_beyond_pml.write_gds()

    filepath_npz = get_sparameters_path(
        component=component,
        dirpath=dirpath,
        layer_stack=layer_stack,
        **settings,
    )
    filepath = filepath_npz.with_suffix(".dat")
    filepath_sim_settings = filepath.with_suffix(".yml")
    filepath_fsp = filepath.with_suffix(".fsp")
    fspdir = filepath.parent / f"{filepath.stem}_s-parametersweep"

    if run and filepath_npz.exists() and not overwrite:
        logger.info(f"Reading Sparameters from {filepath_npz.absolute()!r}")
        return np.load(filepath_npz)

    if not run and session is None:
        print(run_false_warning)

    logger.info(f"Writing Sparameters to {filepath_npz.absolute()!r}")
    x_min = (component_extended.xmin - xmargin) * 1e-6
    x_max = (component_extended.xmax + xmargin) * 1e-6
    y_min = (component_extended.ymin - ymargin) * 1e-6
    y_max = (component_extended.ymax + ymargin) * 1e-6
    index_to_thickness = {}
    index_to_zmin = {}
    for level in layer_stack.layers.values():
        layer = level.layer

        if isinstance(layer, LogicalLayer):
            assert isinstance(layer.layer, tuple | LayerEnum)
            layer_tuple = cast(tuple[int, int], tuple(layer.layer))
        elif isinstance(layer, DerivedLayer):
            assert level.derived_layer is not None
            assert isinstance(level.derived_layer.layer, tuple | LayerEnum)
            layer_tuple = cast(tuple[int, int], tuple(level.derived_layer.layer))
        elif isinstance(layer, tuple):
            # Handle plain tuple layers directly
            layer_tuple = cast(tuple[int, int], layer)
        else:
            raise ValueError(
                f"Layer {layer!r} is not a DerivedLayer, LogicalLayer, or tuple"
            )

        layer_index = int(gf.get_layer(layer_tuple))

        index_to_thickness[layer_index] = level.thickness
        index_to_zmin[layer_index] = level.zmin
    #所以只会处理layerstack和component同时有的layer
    layers_thickness = [
        index_to_thickness[gf.get_layer(layer)]
        for layer in component_with_booleans.layers
        if gf.get_layer(layer) in index_to_thickness
    ]
    if not layers_thickness:
        raise ValueError(
            f"no layers for component {component.layers}in layer stack {layer_stack}"
        )
    layers_zmin = [
        index_to_zmin[gf.get_layer(layer)]
        for layer in component_with_booleans.layers
        if gf.get_layer(layer) in index_to_zmin
    ]
    #component_thickness = max(layers_thickness)   #这里的计算？只选取一个厚度？
    component_thickness=sum(layers_thickness)
    component_zmin = min(layers_zmin)
    #这里只有一层貌似是正确的（zmin还要是0），多层不对,修改*2
    z = (2*component_zmin + component_thickness) / 2 * 1e-6
    z_span = (2 * zmargin + component_thickness) * 1e-6

    x_span = x_max - x_min
    y_span = y_max - y_min

    sim_settings.update(dict(layer_stack=layer_stack.to_dict()))

    sim_settings = dict(
        simulation_settings=sim_settings,
        component=component.to_dict(),
        version=__version__,
    )

    logger.info(
        f"Simulation size = {x_span * 1e6:.3f}, {y_span * 1e6:.3f}, {z_span * 1e6:.3f} um"
    )

    # from pprint import pprint
    # filepath_sim_settings.write_text(yaml.dump(sim_settings))
    # print(filepath_sim_settings)
    # pprint(sim_settings)
    # return

    try:
        import lumapi
    except ModuleNotFoundError as e:
        print(
            "Cannot import lumapi (Python Lumerical API). "
            "You can add set the PYTHONPATH variable or add it with `sys.path.append()`"
        )
        raise e
    except OSError as e:
        raise e

    start = time.time()
    s = session or lumapi.FDTD(hide=True)
    s.newproject()
    s.selectall()
    s.deleteall()
    #s.addrect(    #clad在layerstack设置
    #    x_min=x_min,
    #    x_max=x_max,
    #    y_min=y_min,
    #    y_max=y_max,
    #    z=z,
    #    z_span=z_span,
    #    index=1.5,
    #    name="clad",
    #)

    # Set cladding opacity
    #s.setnamed("clad", "alpha", 0.1)
    #如果不传入就是默认只有si,sio2,sin,传入的话以传入为主
    material_name_to_lumerical_new = material_name_to_lumerical or {}
    material_name_to_lumerical = ss.material_name_to_lumerical.copy()
    material_name_to_lumerical.update(**material_name_to_lumerical_new)

    #material = material_name_to_lumerical[ss.background_material] #sio
    #set_material(session=s, structure="clad", material=material)#字符/数值

    s.addfdtd(
        dimension="3D",
        x_min=x_min,
        x_max=x_max,
        y_min=y_min,
        y_max=y_max,
        z=z,
        z_span=z_span,
        mesh_accuracy=ss.mesh_accuracy,
        use_early_shutoff=True,
        simulation_time=ss.simulation_time,
        simulation_temperature=ss.simulation_temperature,
    )

    exclude_layers = exclude_layers or []
    polygons_per_layer = component_extended_beyond_pml.get_polygons_points(merge=True)

    for level in layer_stack.layers.values():
        layer = level.layer
        mesh_order=level.mesh_order
        if isinstance(layer, LogicalLayer):
            layer_tuple = gf.get_layer_tuple(layer.layer)
        elif isinstance(layer, DerivedLayer):
            layer_tuple = gf.get_layer_tuple(level.derived_layer.layer)
        elif isinstance(layer, tuple):
            # Handle plain tuple layers directly
            layer_tuple = layer
        else:
            raise ValueError(
                f"Layer {layer!r} is not a DerivedLayer, LogicalLayer, or tuple"
            )

        layer_index = int(gf.get_layer(layer_tuple))

        if layer_index in exclude_layers:
            continue

        if layer_index not in polygons_per_layer:
            continue

        zmin = level.zmin

        if zmin is not None:
            thickness = level.thickness
            material_name = layer_to_material[layer]
            if material_name not in material_name_to_lumerical:
                raise ValueError(
                    f"{material_name!r} not in {list(material_name_to_lumerical.keys())}"
                )
            material = material_name_to_lumerical[material_name]

            if layer not in layer_to_zmin:
                raise ValueError(f"{layer} not in {list(layer_to_zmin.keys())}")

            zmin = layer_to_zmin[layer]
            zmax = zmin + thickness
            z = (zmax + zmin) / 2
            sidewall=layer_to_sidewall[layer]
            if sidewall==0 or sidewall is None or sidewall==90:
                s.gdsimport(str(gdspath), f"top_{timestamp}", f"{layer_tuple[0]}:{layer_tuple[1]}")
                layername = f"GDS_LAYER_{layer_tuple[0]}:{layer_tuple[1]}"
                s.setnamed(layername, "z", z * 1e-6)
                s.setnamed(layername, "z span", thickness * 1e-6)

                s.setnamed(f"{layername}::poly", "override mesh order from material database", 1)
                s.setnamed(f"{layername}::poly", "mesh order", mesh_order)
                set_material(session=s, structure=layername, material=material,material_name=material_name)
                if material_name=='sio2':
                    s.setnamed(f"{layername}::poly", "alpha", 0.1)
                logger.info(
                    f"adding {layer_tuple}, thickness = {thickness} um, zmin = {zmin} um "
                )
            else:
                polygons = polygons_per_layer[layer_index]
                poly_name = f"polygon_sidewall_{layer_tuple[0]}_{layer_tuple[1]}"
                # Get vertices from the polygon and convert to um
                poly_vertices = [np.array(polygon) * 1e-6 for polygon in polygons]
                # Add layer builder for sidewall angle
                s.addlayerbuilder()
                s.set({"name": poly_name, "x": 1e-6})
                s.set("x span",300*1e-6)
                s.set("x",0)
                s.set("base mesh order", mesh_order)
                s.set("gds sidewall angle position reference", "Top")
                s.set("geometry", {f"{layer_tuple[0]}:{layer_tuple[1]}": poly_vertices})
                # Add layer
                s.addlayer()
                layer_name = f"poly_layer_{layer_tuple[0]}_{layer_tuple[1]}"
                s.setlayer("default name", "name", layer_name)
                s.setlayer(layer_name, "layer number", f"{layer_tuple[0]}:{layer_tuple[1]}")
                s.setlayer(layer_name, "thickness", thickness * 1e-6)
                s.setlayer(layer_name, "start position", zmin * 1e-6)
                set_material_layer(session, layer_name=layer_name, material=material,material_name=material_name)
                s.setlayer(layer_name, "sidewall angle", sidewall)
          
                logger.info(
                    f"adding {layer_tuple} with sidewall angle {sidewall}°, thickness = {thickness} um, zmin = {zmin} um"
                )
    port_get={}
    for i, port in enumerate(ports):
        port_layer_index = gf.get_layer(port.layer) #同一层有多个component?
        zmin = index_to_zmin[port_layer_index]
        thickness = index_to_thickness[port_layer_index]
        z = zmin + (thickness) / 2       #这里的计算也需要改写(zmin + thickness) / 2
        zspan = 2 * ss.port_margin + thickness

        s.addport()
        p = f"FDTD::ports::port {i + 1}"
        s.setnamed(p, "x", port.x * 1e-6)
        s.setnamed(p, "y", port.y * 1e-6)
        s.setnamed(p, "z", z * 1e-6)
        s.setnamed(p, "z span", zspan * 1e-6)
        s.setnamed(p, "frequency dependent profile", ss.frequency_dependent_profile)
        if ss.frequency_dependent_profile:
            s.setnamed(p, "number of field profile samples", ss.field_profile_samples)

        deg = int(port.orientation)
        # if port.orientation not in [0, 90, 180, 270]:
        #     raise ValueError(f"{port.orientation} needs to be [0, 90, 180, 270]")

        if -45 <= deg <= 45:
            direction = "Backward"
            injection_axis = "x-axis"
            dxp = 0
            dyp = 2 * ss.port_margin + port.dwidth
        elif 45 < deg < 90 + 45:
            direction = "Backward"
            injection_axis = "y-axis"
            dxp = 2 * ss.port_margin + port.dwidth
            dyp = 0
        elif 90 + 45 < deg < 180 + 45:
            direction = "Forward"
            injection_axis = "x-axis"
            dxp = 0
            dyp = 2 * ss.port_margin + port.dwidth
        elif 180 + 45 < deg < 180 + 45 + 90:
            direction = "Forward"
            injection_axis = "y-axis"
            dxp = 2 * ss.port_margin + port.dwidth
            dyp = 0

        else:
            raise ValueError(
                f"port {port.name!r} orientation {port.orientation} is not valid"
            )
        if port.name=='o3':
            direction='Backward'
        s.setnamed(p, "direction", direction)
        s.setnamed(p, "injection axis", injection_axis)
        s.setnamed(p, "y span", dyp * 1e-6)
        s.setnamed(p, "x span", dxp * 1e-6)
        # s.setnamed(p, "theta", deg)
        
        # s.setnamed(p, "name", f"o{i+1}")
        result=set_port_mode_solver_data(s,p,ss.wavelength_start,mode_number=8,selected_mode_type='TE_00',port_type=direction)
        if direction=='Backward':
            port_get['FDTD::ports::'+port.name]=result
        
        s.setnamed(p, "name", port.name)
        logger.info(
            f"port {p} {port.name!r}: at ({port.x}, {port.y}, 0)"
            f"size = ({dxp}, {dyp}, {zspan})"
        )

    s.setglobalsource("wavelength start", ss.wavelength_start * 1e-6)
    s.setglobalsource("wavelength stop", ss.wavelength_stop * 1e-6)
    s.setnamed("FDTD::ports", "monitor frequency points", ss.wavelength_points)

    if run:
        s.save(str(filepath_fsp))
        #s.deletesweep("s-parameter sweep")
        #setresource("FDTD",2,"active",1);GPU resource active
        #s.addsweep(3)
        # s.setsweep("s-parameter sweep", "Excite all ports", 0)
        # s.setsweep("S sweep", "auto symmetry", True)
        
        # #s.runsweep("s-parameter sweep")
        # s.runsweep("s-parameter sweep","GPU") #用GPU计算
        # sp = s.getsweepresult("s-parameter sweep", "S parameters")
        # s.exportsweep("s-parameter sweep", str(filepath))
        # logger.info(f"wrote sparameters to {str(filepath)!r}")

        # sp["wavelengths"] = sp.pop("lambda").flatten() * 1e6
        # np.savez_compressed(filepath, **sp)

        # keys = [key for key in sp.keys() if key.startswith("S")]
        # ra = {
        #     f"{key.lower()}a": list(np.unwrap(np.angle(sp[key].flatten())))
        #     for key in keys
        # }
        # rm = {f"{key.lower()}m": list(np.abs(sp[key].flatten())) for key in keys}
        # results = {"wavelengths": wavelengths}
        # results.update(ra)
        # results.update(rm)
        # df = pd.DataFrame(results, index=wavelengths)
        # df.to_csv(filepath_npz, index=False)
        s.run()
        for port_name,result in port_get.items():
            S,mode_profile_T=get_port_mode_solver_data(s,port_name)
            

        end = time.time()
        sim_settings.update(compute_time_seconds=end - start)
        sim_settings.update(compute_time_minutes=(end - start) / 60)
        filepath_sim_settings.write_text(yaml.dump(sim_settings))
        if delete_fsp_files and fspdir.exists():
            shutil.rmtree(fspdir)
            logger.info(
                f"deleting simulation files in {str(fspdir)!r}. "
                "To keep them, use delete_fsp_files=False flag"
            )

        return sp

    filepath_sim_settings.write_text(yaml.dump(sim_settings))
    return s



def _sample_write_coupler_ring():
    """Write Sparameters when changing a component setting."""
    return [
        write_sparameters_lumerical(
            gf.components.coupler_ring(
                width=width, length_x=length_x, radius=radius, gap=gap
            )
        )
        for width in [0.5]
        for length_x in [0.1, 1, 2, 3, 4]
        for gap in [0.15, 0.2]
        for radius in [5, 10]
    ]


def _sample_bend_circular():
    """Write Sparameters for a circular bend with different radius."""
    return [
        write_sparameters_lumerical(gf.components.bend_circular(radius=radius))
        for radius in [2, 5, 10]
    ]


def _sample_bend_euler():
    """Write Sparameters for a euler bend with different radius."""
    return [
        write_sparameters_lumerical(gf.components.bend_euler(radius=radius))
        for radius in [2, 5, 10]
    ]


def _sample_convergence_mesh():
    return [
        write_sparameters_lumerical(
            component=gf.components.straight(length=2),
            mesh_accuracy=mesh_accuracy,
        )
        for mesh_accuracy in [1, 2, 3]
    ]


def _sample_convergence_wavelength():
    return [
        write_sparameters_lumerical(
            component=gf.components.straight(length=2),
            wavelength_start=wavelength_start,
        )
        for wavelength_start in [1.2, 1.4]
    ]


if __name__ == "__main__":
    # import lumapi
    # s = lumapi.FDTD()

    # component = gf.components.straight(length=2.5)
    component = gf.components.mmi1x2()

    material_name_to_lumerical = dict(si=(3.45, 2))  # or dict(si=3.45+2j)
    r = write_sparameters_lumerical(
        component=component,
        material_name_to_lumerical=material_name_to_lumerical,
        run=False,
        # session=s,
    )
    # c = gf.components.coupler_ring(length_x=3)
    # c = gf.components.mmi1x2()
    # print(r)
    # print(r.keys())
    # print(component.ports.keys())
